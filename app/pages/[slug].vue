<script setup lang="ts">
import { findPageHeadline } from "@nuxt/ui-pro/utils/content";

const { throw404Error } = useErrorHandling();

definePageMeta({
  layout: "docs"
});

const route = useRoute();
const { data: navigation } = await useAsyncData("navigation", () =>
  queryCollectionNavigation("content")
);

const { data: page } = await useAsyncData(route.path, () =>
  queryCollection("content").path(route.path).first()
);

if (!page?.value) {
  throw404Error();
}

useSeoMeta({
  title: page.value?.seo?.title,
  ogTitle: page.value?.seo?.title,
  description: page.value?.seo?.description,
  ogDescription: page.value?.seo?.description
});

const headline = computed(() => findPageHeadline(navigation?.value, page.value));
</script>

<template>
  <UMain>
    <UPage v-if="page" class="mt-2">
      <div>
        <FiltersCategories />
      </div>
      <UPageHeader
        :title="page.title"
        :description="page.description"
        :links="page.links"
        :headline="headline"
      />

      <UPageBody>
        <ContentRenderer v-if="page" :value="page" />
      </UPageBody>

      <template v-if="page?.body?.toc?.links?.length" #right>
        <UContentToc
          :ui="{ root: 'top-0 z-10', container: 'lg:py-2' }"
          title="Оглавление"
          :links="page.body?.toc?.links"
        />
      </template>
    </UPage>
  </UMain>
</template>

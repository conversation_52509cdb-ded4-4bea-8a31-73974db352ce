<template>
  <div class="space-y-4">
    <!-- Digest list card -->
    <ArticleDigest />

    <section>
      <div class="grid grid-cols-1 gap-2 lg:grid-cols-3">
        <div v-for="post in gunPosts.data" :key="post.slug">
          <CardItemSmall :item="post" />
        </div>
      </div>
    </section>

    <section class="space-y-4">
      <ArticleFeedCard v-for="post in news?.data" :key="post.slug" :post="post" />
    </section>

    <Pagination
      :meta="news.meta"
      :page="page"
      :disabled="status === 'pending'"
      @set-page="page = $event"
    />
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "journal"
});

const client = useSanctumClient();
const route = useRoute();
const page = ref(1);

const { data: gunPosts } = await useAsyncData("posts:limit:6", () =>
  client("/posts?limit=6")
);

const { data: news, status } = await useAsyncData(
  `blog:articles:news:${route.query?.page ?? 1}`,
  () =>
    client("/blog/articles", {
      params: {
        page: route.query?.page ?? 1,
        ...route.query
      }
    }),
  {
    watch: [route]
  }
);


</script>

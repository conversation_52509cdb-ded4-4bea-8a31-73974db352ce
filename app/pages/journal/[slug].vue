<template>
  <div class="rounded-xl bg-(--ui-bg-elevated)/25 p-4 ring ring-(--ui-border-accented)/50">
    <div class="prose dark:prose-invert prose-p:m-0 prose-ul:m-0 prose-h2:my-4 max-w-none">
      <div class="mb-4 flex flex-wrap items-center gap-2 text-sm">
        <UAvatar :src="data.article.user?.avatar?.src" :alt="data.article.user.name" size="xs" />
        <span class="font-medium">{{ data.article.user.name }}</span>
        <span class="text-neutral-500">
          <span>{{ data.article?.category?.name }}</span> ·
          <NuxtTime date-style="long" time-style="short" :datetime="data.article.published_at" />
        </span>
      </div>

      <h1>
        {{ data.article.title }}
      </h1>

      <div v-html="data.article.content" />
    </div>
  </div>


  <CommentsList :article-slug="data.article.slug" />
</template>

<script setup lang="ts">
definePageMeta({
  layout: "journal"
});

const route = useRoute();
const client = useSanctumClient();

const { data } = await useAsyncData(`article:${route.params.slug}`, () =>
  client(`/blog/articles/${route.params.slug}`)
);
</script>

<style></style>

<template>
  <div>
    <ClientOnly class="my-6">
      <UModal :ui="{ overlay: 'bg-(--ui-bg)/75 backdrop-blur', content: 'sm:max-w-4xl' }">
        <UButton label="Open" color="neutral" variant="subtle" />
        <template #header>
          <UUser size="2xl" :name="user.name" :avatar="{ src: user.avatar.src }">
            <template #description>
              <USelectMenu
                v-model="value"
                size="xs"
                variant="soft"
                :items="items"
                :search-input="false"
                class="w-48"
              />
            </template>
          </UUser>
        </template>
        <template #body>
          <UFormField size="xl" required name="title" class="pl-[2.9em]">
            <UInput
              v-model="state.title"
              autofocus
              :maxlength="60"
              aria-describedby="character-count"
              :ui="{ trailing: 'pointer-events-none' }"
              class="w-full"
              variant="none"
              placeholder="Заголовок"
            >
              <template #trailing>
                <div
                  id="character-count"
                  class="text-xs text-(--ui-text-muted) tabular-nums"
                  aria-live="polite"
                  role="status"
                >
                  {{ state.title?.length ?? 0 }}/60
                </div>
              </template>
            </UInput>
          </UFormField>

          <Editor2 v-model="state.source_content" placeholder="Какой-токонтент" />
        </template>
        <template #footer="{ close }">
          <UButton size="xl" :loading="status === 'pending'" @click="save">Опубликовать</UButton>
        </template>
      </UModal>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import type { User } from "~/types/auth";

definePageMeta({
  layout: "journal"
});

const user = useSanctumUser<User>();
const route = useRoute();
const client = useSanctumClient();
console.log(`/blog/articles/${route.params.slug}`);
const { data, status, refresh } = await useAsyncData(`article:edit:${route.params.slug}`, () =>
  client(`/blog/articles/${route.params.slug}`)
);

const items = ref(["Новости", "Личный опыт", "Охота", "Право"]);
const value = ref("Новости");

console.log("title", data.value?.article?.title);

const state = reactive({
  title: "",
  source_content: {}
});

const save = async () => {
  try {
    await client(`/blog/articles/nuzny-li-rossii-obshhedostupnye-strelbishha-iniciativa-na-roi`, {
      method: "PUT",
      body: {
        content: state.source_content
      }
    });
    await refresh();
  } catch (error) {
    useSanctumError(error);
  }
};
</script>

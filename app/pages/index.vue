<template>
  <UContainer class="py-3 md:py-4">
    <div class="flex flex-col gap-4 lg:flex-row lg:gap-6">
      <!-- Основной контент -->
      <div class="w-full">
        <!-- Фильтры и алерты -->
        <div class="space-y-3">
          <FiltersCategories />

          <!-- Теги (если нужны) -->
          <div v-if="false" class="flex flex-wrap gap-1.5">
            <UButton size="xs" color="neutral" variant="soft">Пистолет пм</UButton>
            <UButton size="xs" color="neutral" variant="soft">Охолощенное оружие</UButton>
            <UButton size="xs" color="neutral" variant="soft">Травматический пистолет</UButton>
            <UButton size="xs" color="neutral" variant="soft">Купить карабин для охоты</UButton>
            <UButton size="xs" color="neutral" variant="soft">Вепрь 12 молот</UButton>
          </div>

          <AlertsBase />
        </div>

        <!-- SEO заголовок и описание -->
        <div v-if="posts?.seo?.title || posts?.seo?.content" class="mt-4 space-y-2">
          <h1 v-if="posts?.seo?.title" class="text-xl leading-tight font-bold md:text-2xl">
            {{ posts.seo.title }}
          </h1>
          <p v-if="posts?.seo?.content" class="text-sm text-[var(--ui-text-muted)]">
            {{ posts.seo.content }}
          </p>
        </div>

        <!-- Секция рекомендаций -->
        <div class="mt-4 space-y-3">
          <!-- Заголовок и сортировка -->
          <div
            class="flex flex-col gap-2 border-t border-[var(--ui-border-muted)] pt-3 sm:flex-row sm:items-center sm:justify-between"
          >
            <h2 class="text-base font-semibold">Рекомендации для вас</h2>
            <FiltersOrderBy />
          </div>

          <!-- Сетка карточек -->
          <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
            <template v-if="status === 'pending'">
              <Skeleton v-for="i in 21" :key="i" />
            </template>
            <template v-else>
              <CardItem
                v-for="(item, n) in posts?.data"
                :key="`card-${n}-${item.id}`"
                :position="n"
                :item="item"
              />
            </template>
          </div>

          <!-- Пагинация -->
          <LazyPagination
            :meta="posts?.meta"
            :disabled="status === 'pending'"
            class="mt-4"
            @next-page="nextPage"
          />
        </div>
      </div>

      <!-- Боковая панель -->
      <aside class="w-full space-y-4 lg:w-110 lg:shrink-1">
        <!-- Новости -->
        <NewsWidget :limit="10" />

        <!-- Популярные обсуждения -->
        <div v-if="false" class="space-y-3">
          <h3 class="text-base font-semibold">Популярные обсуждения</h3>

          <div class="space-y-2">
            <!-- Карточка обсуждения -->
            <ULink
              to="/"
              class="block space-y-2 rounded-lg bg-gray-50 p-3 transition-colors hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700"
            >
              <h4 class="line-clamp-2 text-sm leading-tight font-medium">
                Вопрос по эволюции патронов кольцевого воспламенения
              </h4>
              <p class="line-clamp-2 text-xs text-[var(--ui-text-muted)]">
                Поиск по форуму нашел, что в ранних патронах Флобера не было выраженной закраины, а
                только расширение, чтобы патрон не проваливался в ствол и было чего сминать курку
              </p>
              <UUser
                size="2xs"
                name="Ротмистр Чачу"
                description="28-6-2024 12:38"
                :avatar="{ src: 'https://i.pravatar.cc/40?u=Ротмистр Чачу' }"
              />
            </ULink>

            <ULink
              to="/"
              class="block space-y-2 rounded-lg bg-gray-50 p-3 transition-colors hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700"
            >
              <h4 class="line-clamp-2 text-sm leading-tight font-medium">
                Перествол ВПО-209 366ТКМ в СКС 7, 62
              </h4>
              <p class="line-clamp-2 text-xs text-[var(--ui-text-muted)]">
                Имею такой балласт в виде ВПО-209 который в свое время взял для стажа. В этой связи
                вопрос - возможна ли его легальная модернизация до
              </p>
              <UUser
                size="2xs"
                name="jacker2000"
                description="3-2-2025 12:03"
                :avatar="{ src: 'https://i.pravatar.cc/40?u=jacker2000' }"
              />
            </ULink>
          </div>

          <UButton size="xs" block variant="soft"> Все обсуждения </UButton>
        </div>

        <!-- VIP объявления -->
        <LazyAdsVip v-if="posts?.vipAds?.length" :items="posts?.vipAds" />
      </aside>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
const client = useSanctumClient();
const route = useRoute();

const { data: posts, status } = await useAsyncData(
  `posts:${route.query?.page ?? 1}`,
  () =>
    client("/posts", {
      params: {
        page: route.query?.page ?? 1,
        ...route.query
      }
    }),
  {
    watch: [route]
  }
);

useSeoMeta({
  title: posts.value?.seo?.title,
  ogTitle: posts.value?.seo?.title,
  description: posts.value?.seo?.meta_description,
  ogDescription: posts.value?.seo?.meta_description
});

const nextPage = async () => {
  const nextPage = posts.value.meta.current_page + 1;
  const response = await client("/posts", {
    params: {
      ...route.query,
      page: nextPage
    }
  });

  if (response?.data?.length) {
    posts.value = {
      data: [...posts.value.data, ...response.data],
      meta: response.meta,
      vipAds: response.vipAds
    };
  }
};
</script>

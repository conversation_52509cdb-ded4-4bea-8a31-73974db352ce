<template>
  <article
    class="rounded-xl bg-(--ui-bg-elevated)/25 p-4 ring ring-(--ui-border-accented)/50"
  >
    <!-- author line -->
    <div class="flex items-center justify-between">
      <div class="flex flex-wrap items-center gap-2 text-sm">
        <UAvatar :src="post.user?.avatar?.src" :alt="post.user.name" size="xs" />
        <span class="font-medium">{{ post.user.name }}</span>
        <span class="text-neutral-500">
            <span>{{ post?.category?.name }}</span> ·
            <NuxtTime date-style="long" time-style="short" :datetime="post.published_at" />
          </span>
      </div>
      <UButton size="xs" variant="soft" color="neutral" class="rounded-full">
        Подписаться
      </UButton>
    </div>

    <NuxtLink :to="{ name: 'journal-slug', params: { slug: post.slug } }">
      <h2 class="mt-2 text-2xl leading-snug font-bold">{{ post.title }}</h2>

      <p v-if="post.excerpt" class="mt-1 text-neutral-700 dark:text-neutral-300">
        {{ post.excerpt }}
      </p>

      <span v-if="post?.cover_image" class="mt-3 overflow-hidden rounded-xl">
          <img :src="post.cover_image" :alt="post.title" class="w-full object-cover" />
        </span>
    </NuxtLink>

    <div
      class="mt-3 flex flex-wrap items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400"
    >
        <span class="inline-flex items-center gap-1"
        ><UIcon name="i-lucide-arrow-big-up" class="h-4 w-4" />
          {{ formatNumber(post.upvotes) }}</span
        >
      <span class="inline-flex items-center gap-1"
      ><UIcon name="i-lucide-message-circle" class="h-4 w-4" />
          {{ formatNumber(post.comments) }}</span
      >
      <span class="inline-flex items-center gap-1"
      ><UIcon name="i-lucide-clock-8" class="h-4 w-4" /> {{ post.readingTime }} мин</span
      >
    </div>
  </article>
</template>

<script setup lang="ts">
defineProps({
  post: Object
});
const formatNumber = (n: number) => new Intl.NumberFormat("ru-RU").format(n);
</script>

<script setup lang="ts">
const { isSearchOpen } = useDashboard();
const { isAuthenticated } = useSanctumAuth();

defineProps({
  search: {
    type: Boolean,
    default: true
  },
  addButton: {
    type: Boolean,
    default: true
  }
});
</script>

<template>
  <UHeader
    to="/"
    :ui="{
      center: 'w-full',
      root: 'bg-(--ui-bg)/75 backdrop-blur border-b border-(--ui-border) h-(--ui-header-height)  !relative'
    }"
  >
    <template #toggle>
      <span></span>
    </template>
    <template #title>
      <Logo />
    </template>

    <template #default>
      <Search v-if="search" />
      <div v-else class="w-1" />
    </template>

    <template #right>
      <div class="flex items-center gap-1.5 md:gap-2">
        <Region />
        <!--        <UColorModeButton /> -->
        <UButton
          v-if="search"
          color="neutral"
          variant="outline"
          class="lg:hidden"
          icon="i-lucide-search"
          aria-label="Быстрый поиск"
          @click="isSearchOpen = true"
        />
        <AuthUser v-if="isAuthenticated" />
        <AuthLogin v-else />
        <UButton
          v-if="addButton"
          to="/add"
          color="error"
          class="hidden justify-center bg-red-800 hover:bg-red-900 lg:flex lg:min-w-40 dark:text-white"
        >
          Подать объявление
        </UButton>
        <UButton
          v-if="addButton"
          icon="i-lucide-plus"
          to="/add"
          color="error"
          square
          class="bg-red-800 hover:bg-red-900 lg:hidden dark:text-white"
          aria-label="Подать объявление"
        />
      </div>
    </template>
  </UHeader>
</template>

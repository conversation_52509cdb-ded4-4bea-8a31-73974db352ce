<template>
  <div ref="htmlelement" class="editorjs min-h-[300px]"></div>
</template>

<script setup lang="ts">
import EditorJS from "@editorjs/editorjs";
import ListTool from "@editorjs/list";
import ImageTool from "@editorjs/image";

const client = useSanctumClient();
const formData = new FormData();
const htmlelement = ref(null);

const props = defineProps({
  modelValue: {
    type: [Object, String],
    default: null
  },
  placeholder: String,
  readOnly: Boolean
});
const emit = defineEmits(["update:modelValue"]);

let editor;
let updatingModel = false;

// model -> view
function modelToView() {
  if (!props.modelValue) {
    return;
  }
  if (typeof props.modelValue === "string") {
    editor.blocks.renderFromHTML(props.modelValue);
    return;
  }

  editor.render(props.modelValue);
}

// view -> model
function viewToModel(api, event) {
  if (props.readOnly) {
    return;
  }
  updatingModel = true;

  editor
    .save()
    .then((outputData) => {
      console.log(event, "Saving completed: ", outputData);
      emit("update:modelValue", outputData);
    })
    .catch((error) => {
      console.log(event, "Saving failed: ", error);
    })
    .finally(() => {
      updatingModel = false;
    });
}

onMounted(() => {
  editor = new EditorJS({
    holder: htmlelement.value,
    placeholder: props.placeholder,
    inlineToolbar: ["bold", "italic", "link"],
    tools: {
      // embed: EmbedTool,
      list: ListTool,
      image: {
        class: ImageTool,
        config: {
          /**
           * Custom uploader
           */
          uploader: {
            /**
             * Upload file to the server and return an uploaded image data
             * @param {File} file - file selected from the device or pasted by drag-n-drop
             * @return {Promise.<{success, file: {url}}>}
             */
            async uploadByFile(file) {
              // your own uploading logic here
              formData.append("image", file);
              return await client(`/blog/upload`, {
                method: "POST",
                body: formData
              });
            },

            /**
             * Send URL-string to the server. Backend should load image by this URL and return an uploaded image data
             * @param {string} url - pasted image URL
             * @return {Promise.<{success, file: {url}}>}
             */
            uploadByUrl(url) {
              // your ajax request for uploading
              console.log(url);
            }
          }
        }
      }
      // video: VideoTool,
    },
    /**
     * Internationalzation config
     */
    i18n: {
      /**
       * @type {I18nDictionary}
       */
      messages: {
        /**
         * Other below: translation of different UI components of the editor.js core
         */
        ui: {
          blockTunes: {
            toggler: {
              "Click to tune": "Нажмите, чтобы настроить",
              "or drag to move": "или перетащите"
            }
          },
          inlineToolbar: {
            converter: {
              "Convert to": "Конвертировать в"
            }
          },
          toolbar: {
            toolbox: {
              Add: "Добавить"
            }
          }
        },

        toolNames: {
          Text: "Параграф",
          Heading: "Заголовок",
          List: "Список",
          Warning: "Примечание",
          Checklist: "Чеклист",
          Quote: "Цитата",
          Code: "Код",
          Delimiter: "Разделитель",
          "Raw HTML": "HTML-фрагмент",
          Table: "Таблица",
          Link: "Ссылка",
          Marker: "Маркер",
          Bold: "Полужирный",
          Italic: "Курсив",
          InlineCode: "Моноширинный",
          "Unordered List": "Обычный список",
          "Ordered List": "Нумерованный список",
          Image: "Загрузить картинку"
        },

        /**
         * Section for passing translations to the external tools classes
         */
        tools: {
          warning: {
            // <-- 'Warning' tool will accept this dictionary section
            Title: "Название",
            Message: "Сообщение"
          },
          link: {
            "Add a link": "Вставьте ссылку"
          },
          stub: {
            "The block can not be displayed correctly.": "Блок не может быть отображен"
          },
          image: {
            "Select an Image": "Выбрать картинку",
            Caption: "Подсказка или источник картинки",
            "With border": "С рамкой",
            "Stretch image": "Растянуть картинку",
            "With background": "С фоном, по центру"
          }
        },

        blockTunes: {
          delete: {
            Delete: "Удалить"
          },
          moveUp: {
            "Move up": "Переместить вверх"
          },
          moveDown: {
            "Move down": "Переместить вниз"
          }
        }
      }
    },
    readOnly: props.readOnly,
    minHeight: "auto",
    data: props.modelValue,
    onReady: modelToView,
    onChange: viewToModel
  });
});

watch(
  () => props.modelValue,
  () => {
    if (!updatingModel) {
      modelToView();
    }
  }
);

onUnmounted(() => {
  editor.destroy();
});
</script>

<template>
  <ClientOnly>
    <UButton label="Написать" block icon="i-lucide-pencil" color="info" variant="subtle" @click="open = true" />
    <UModal
      v-model:open="open"
      :dismissible="false"
      :ui="{ overlay: 'bg-(--ui-bg)/75 backdrop-blur', content: 'sm:max-w-334xl', header: 'px-3 py-6', body: 'p-3 sm:p-3', footer: 'p-3' }"
      :fullscreen="fullScreen"
    >
      <template #header>
        <div class="flex w-full justify-between">
          <UUser size="2xl" :name="user.name" :avatar="{ src: user.avatar.src }">
            <template #description>
              <USelectMenu
                v-model="state.category"
                size="xs"
                variant="soft"
                :items="items"
                :search-input="false"
                class="w-48"
              />
            </template>
          </UUser>
          <div>
            <UButton
              size="sm"
              color="neutral"
              variant="ghost"
              :icon="fullScreen ? 'i-lucide-minimize-2' : 'i-lucide-maximize-2'"
              @click="fullScreen = !fullScreen"
            />
          </div>
        </div>
      </template>
      <template #body>
        <UFormField size="xl" required name="title">
          <UInput
            v-model="state.title"
            autofocus
            :maxlength="60"
            aria-describedby="character-count"
            :ui="{ trailing: 'pointer-events-none' }"
            class="w-full"
            variant="none"
            placeholder="Заголовок"
          >
            <template #trailing>
              <div
                id="character-count"
                class="text-xs text-(--ui-text-muted) tabular-nums"
                aria-live="polite"
                role="status"
              >
                {{ state.title?.length ?? 0 }}/60
              </div>
            </template>
          </UInput>
        </UFormField>

        <Editor2 v-model="state.source_content" placeholder="Какой-токонтент" />
      </template>
      <template #footer>
        <UButton color="neutral" variant="ghost" @click="close">Отменить</UButton>
        <UButton @click="close">Опубликовать</UButton>
      </template>
    </UModal>
  </ClientOnly>
</template>

<script setup lang="ts">
import type { User } from "~/types/auth";
import { ModalsAlert } from "#components";

const user = useSanctumUser<User>();
const overlay = useOverlay();
const open = ref(false);
const fullScreen = ref(false);
const items = ref(["Новости", "Личный опыт", "Охота", "Право"]);
const state = reactive({
  title: "",
  category: "Новости",
  source_content: {}
});
const close = () => {
  overlay
    .create(ModalsAlert, {
      props: {
        title: "Вы точно хотите закрыть?",
        description: "Все ваши изменения будут безвозвратно потеряны",
        actions: [
          {
            label: "Да, все хорошо",
            color: "error",
            icon: "i-lucide-x",
            onClick: () => {
              open.value = false;
            }
          }
        ]
      }
    })
    .open();
}
</script>

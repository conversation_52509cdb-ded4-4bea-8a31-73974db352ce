<template>
  <NuxtLink
    :to="{ name: 'post', params: { slug: item.slug, category: item.category } }"
    class="bg-elevated hover:bg-accented/75 flex h-full w-full flex-col space-y-2 rounded-xl p-4 text-left text-sm transition-colors"
    :class="{
      'rounded-md bg-yellow-100 p-2 hover:bg-yellow-50 dark:bg-yellow-950 dark:hover:bg-yellow-900':
        hasColorPromo
    }"
  >
    <span class="flex gap-2">
      <img
        v-if="item?.thumbnails?.[0]"
        :src="item.thumbnails?.[0]"
        class="h-12 w-12 shrink-0 rounded-md object-cover"
        width="48"
        height="48"
        :alt="item.title"
      />
      <span class="space-y-1 px-0.5">
        <span
          class="inline text-lg font-semibold md:line-clamp-2 md:text-sm"
          :class="{ 'text-yellow-950 dark:text-yellow-100': hasColorPromo }"
        >
          {{ item.title }}
        </span>
        <span class="block text-lg md:text-sm">
          {{ $currency(item.price) }}
        </span>
      </span>
    </span>
    <span class="space-y-2">
      <span
        class="text-md mb-auto line-clamp-4 text-[var(--ui-text-muted)] md:line-clamp-2 md:text-xs"
      >
        {{ item.description_short }}
      </span>
      <span
        v-if="item?.address ?? item?.city_name"
        class="line-clamp-4 block text-[var(--ui-text-muted)] md:line-clamp-2 md:text-xs"
      >
        <UIcon name="i-lucide-map-pin" class="size-2.5" />
        {{ item.address ?? item.city_name }}
      </span>
    </span>
    <span
      v-if="item?.published_at"
      class="mt-auto mb-0 flex items-center gap-1 text-sm text-[var(--ui-text-muted)] md:text-xs"
    >
      <UIcon name="i-lucide-clock" />
      <NuxtTime :datetime="item.published_at" date-style="medium" time-style="short" />
    </span>
  </NuxtLink>
</template>

<script setup lang="ts">
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  small: {
    type: Boolean,
    default: false
  },
  position: {
    type: Number,
    default: 0
  }
});

const { $currency } = useNuxtApp();

const hasColorPromo = computed(() => props.item?.promotion?.["color"]);
const hasVipPromo = computed(() => props.item?.promotion?.["vip"]);
</script>

<template>
  <UButton
    :variant="active ? 'soft' : 'ghost'"
    size="xs"
    :color="active ? 'primary' : 'neutral'"
    @click="$emit('click')"
  >
    <Uicon v-if="icon" :name="icon" />
    <span v-else class="text-base leading-none">{{ emoji }}</span>
    <span class="tabular-nums">{{ count }}</span>
  </UButton>
</template>

<script setup lang="ts">
defineProps<{
  icon?: string
  emoji?: string
  count: number
  active?: boolean
  aria?: string
}>()

defineEmits<{ (e: 'click'): void }>()
</script>

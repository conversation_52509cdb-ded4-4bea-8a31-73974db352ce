<template>
  <div class="flex gap-3">
    <UAvatar size="lg" :alt="comment.user.name" :text="initials(comment.user.name)" />

    <div class="flex-1">
      <!-- Хедер -->
      <div class="flex items-center gap-2">
        <div class="font-medium">{{ comment.user.name }}</div>
<!--        <div v-if="comment.user.handle" class="text-(&#45;&#45;ui-text-muted)text-sm">{{ comment.user.handle }}</div>-->
        <div class="text-(--ui-text-muted)text-sm">·
          <NuxtTime :datetime="comment.created_at" date-style="medium" time-style="short" />
        </div>
      </div>

      <!-- Текст -->
      <div class="mt-1 whitespace-pre-line leading-relaxed">
        {{ comment.text }}
      </div>

      <!-- Действия -->
      <div v-if="false" class="mt-2 flex items-center gap-2">
        <CommentsReactionChip
          emoji="🤯"
          :count="comment.reactions.wow"
          :active="comment.myReaction === 'wow'"
          aria="Реакция: вау"
          @click="$emit('react', comment.id, 'wow')"
        />
        <CommentsReactionChip
          emoji="❤️"
          :count="comment.reactions.heart"
          :active="comment.myReaction === 'heart'"
          aria="Реакция: сердечко"
          @click="$emit('react', comment.id, 'heart')"
        />
        <CommentsReactionChip
          icon="i-heroicons-plus-small"
          :count="comment.reactions.plus"
          :active="comment.myReaction === 'plus'"
          aria="Реакция: плюс"
          @click="$emit('react', comment.id, 'plus')"
        />

        <UDropdownMenu :items="[[{ label: 'Скопировать ссылку' }, { label: 'Пожаловаться' }]]">
          <UButton icon="i-heroicons-ellipsis-horizontal" variant="ghost" size="xs" />
        </UDropdownMenu>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  comment: any
}>()

defineEmits<{
  (e: 'react', id: number, kind: 'wow' | 'heart' | 'plus'): void
  (e: 'reply', id: number): void
}>()


function initials(name: string) {
  return name.split(' ').map(n => n[0]).join('').slice(0, 2).toUpperCase()
}
</script>

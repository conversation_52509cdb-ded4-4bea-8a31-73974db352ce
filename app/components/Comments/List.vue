<template>
  <UCard class="w-full mt-6 space-y-4">
    <div class="text-xl font-semibold">{{ $declOfNumString(comments.meta?.total, ['комментарий', 'комментария', 'комментариев']) }} </div>


    <!-- Поле ввода -->
    <div class="w-full mt-4">
      <UTextarea v-model="draft" class="w-full" placeholder="Комментарий…" autoresize :rows="3" />
      <UButton @click="sendComment">Отправить</UButton>
    </div>

    <!-- Список комментариев -->
    <div class="space-y-6 mt-6">
      <CommentsCommentItem
        v-for="c in comments.data"
        :key="c.id"
        :comment="c"
        @react="onReact"
        @reply="onReply"
      />
    </div>
  </UCard>
</template>

<script setup lang="ts">
const props = defineProps<{
  articleSlug: string;
}>();
const client = useSanctumClient();
const draft = ref("");

const visibleCount = ref(2);
const pageSize = 3;

const { data: comments, refresh } = await useAsyncData(`article:${props.articleSlug}:comments`, () =>
  client(`/blog/articles/${props.articleSlug}/comments`)
);


function loadMore() {
  visibleCount.value = Math.min(visibleCount.value + pageSize, comments.value?.data?.length);
}


function onReact(commentId: number, kind: any) {
  console.log({ commentId, kind })
}

function onReply(commentId: number) {
  draft.value = `@id:${commentId} `;
}

async function sendComment() {
  if (!draft.value.trim()) return;
  try {
    await client(`/blog/articles/${props.articleSlug}/comments`, {
      method: "POST",
      body: {
        body: draft.value.trim()
      }
    });
    draft.value = "";
    await refresh();
  } catch (error) {
    useSanctumError(error);
  }
}
</script>

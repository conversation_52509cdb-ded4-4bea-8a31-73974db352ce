<template>
  <div class="space-y-3">
    <!-- Заголовок (опционально) -->
    <h3 v-if="showTitle" class="text-base font-semibold">Новости и статьи</h3>

    <!-- Список новостей -->
    <NuxtLink
      v-for="item in topics?.data"
      :key="`news-${item.slug}`"
      :to="{ name: 'news-slug', params: { slug: item.slug } }"
      class="bg-elevated hover:bg-accented/75 block space-y-2 rounded-lg p-3 transition-colors"
    >
      <!-- Заголовок с изображением -->
      <div class="flex gap-2.5">
        <h4 class="line-clamp-2 text-sm leading-tight font-medium">
          {{ item.title }}
        </h4>
      </div>

      <UUser
        size="2xs"
        :avatar="{
          src: item.last_post.user.avatar.src
        }"
        :name="item.last_post.user.name"
      />

      <!-- Описание -->
      <p class="line-clamp-2 text-xs text-gray-600 dark:text-gray-400">
        {{ item.last_post?.content }}
      </p>

      <div class="flex gap-1.5 text-xs text-gray-500">
        <span>Ответов: {{ item.posts_count }}</span>
        <span>Просмотров: {{ item.views_count }}</span>
      </div>
    </NuxtLink>

    <!-- Кнопка -->
    <UButton to="/news" size="sm" block variant="soft"> Все новости </UButton>
  </div>
</template>

<script setup lang="ts">
import type { NewsResponse } from "~/types/news";

interface Props {
  limit?: number;
  showTitle?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  limit: 5,
  showTitle: false
});

const client = useSanctumClient();

const { data: topics } = await useAsyncData<NewsResponse>(`forum-widget-${props.limit}`, () =>
  client("/forum/topics", {
    params: {
      limit: props.limit
    }
  })
);
</script>

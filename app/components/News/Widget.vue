<template>
  <div class="space-y-3">
    <!-- Заголовок (опционально) -->
    <h3 v-if="showTitle" class="text-base font-semibold">Новости и статьи</h3>

    <!-- Список новостей -->
    <NuxtLink
      v-for="(item, index) in news?.data"
      :key="`news-${item.id || index}`"
      :to="{ name: 'news-slug', params: { slug: item.slug } }"
      class="bg-elevated hover:bg-accented/75 block space-y-2 rounded-lg p-3"
    >
      <!-- Заголовок с изображением -->
      <div class="flex gap-2.5">
        <img
          v-if="item.cover_small"
          :src="item.cover_small"
          :alt="item.title"
          class="h-8 w-8 shrink-0 rounded object-cover"
          loading="lazy"
        />
        <h4 class="line-clamp-2 text-sm leading-tight font-medium">
          {{ item.title }}
        </h4>
      </div>

      <!-- Описание -->
      <p class="line-clamp-2 text-xs text-gray-600 dark:text-gray-400">
        {{ item.description }}
      </p>

      <!-- Дата (опционально) -->
      <div v-if="item.published_at" class="text-xs text-gray-500">
        <NuxtTime :datetime="item.published_at" date-style="medium" />
      </div>
    </NuxtLink>

    <!-- Кнопка -->
    <UButton to="/news" size="sm" block variant="soft"> Все новости </UButton>
  </div>
</template>

<script setup lang="ts">
import type { NewsResponse } from "~/types/news";

interface Props {
  limit?: number;
  showTitle?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  limit: 5,
  showTitle: false
});

const client = useSanctumClient();

const { data: news } = await useAsyncData<NewsResponse>(`news-widget-${props.limit}`, () =>
  client("/news", {
    params: {
      limit: props.limit
    }
  })
);
</script>

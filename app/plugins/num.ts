export default defineNuxtPlugin(() => {
  return {
    provide: {
      declOfNum: function (number: number, titles: string[]): string {
        const cases = [2, 0, 1, 1, 1, 2] as const
        const mod10 = number % 10
        const mod100 = number % 100
        const caseIndex = mod100 > 4 && mod100 < 20 ? 2 : cases[mod10 < 5 ? mod10 : 5] as number
        return titles[caseIndex] ?? titles[0] ?? ''
      },

      declOfNumString: function (number: number, titles: string[]): string {
        const cases = [2, 0, 1, 1, 1, 2] as const
        const mod10 = number % 10
        const mod100 = number % 100
        const caseIndex = mod100 > 4 && mod100 < 20 ? 2 : cases[mod10 < 5 ? mod10 : 5] as number
        const title = titles[caseIndex] ?? titles[0] ?? ''
        return number + ' ' + title
      }
    }
  };
});

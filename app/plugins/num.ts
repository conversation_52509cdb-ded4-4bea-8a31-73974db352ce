export default defineNuxtPlugin(() => {
  return {
    provide: {
      declOfNum: function (number: number, titles: string[]): string {
        const cases = [2, 0, 1, 1, 1, 2] as const
        const caseIndex = number % 100 > 4 && number % 100 < 20 ? 2 : cases[number % 10 < 5 ? number % 10 : 5]
        return titles[caseIndex] ?? titles[0] ?? ''
      },

      declOfNumString: function (number: number, titles: string[]): string {
        const cases = [2, 0, 1, 1, 1, 2] as const
        const caseIndex = number % 100 > 4 && number % 100 < 20 ? 2 : cases[number % 10 < 5 ? number % 10 : 5]
        const title = titles[caseIndex] ?? titles[0] ?? ''
        return number + ' ' + title
      }
    }
  };
});

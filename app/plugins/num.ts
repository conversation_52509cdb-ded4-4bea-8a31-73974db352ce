export default defineNuxtPlugin(() => {
  return {
    provide: {
      declOfNum: function (number: number, titles: string[]): string {
        const cases = [2, 0, 1, 1, 1, 2]
        const caseIndex = number % 100 > 4 && number % 100 < 20 ? 2 : cases[number % 10 < 5 ? number % 10 : 5]!
        return titles[caseIndex] || titles[0]
      },

      declOfNumString: function (number: number, titles: string[]): string {
        const cases = [2, 0, 1, 1, 1, 2]
        const caseIndex = number % 100 > 4 && number % 100 < 20 ? 2 : cases[number % 10 < 5 ? number % 10 : 5]!
        return number + ' ' + (titles[caseIndex] || titles[0])
      }
    }
  };
});

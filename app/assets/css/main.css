@import "tailwindcss";
@import "@nuxt/ui-pro";
@import "vue3-carousel/dist/carousel.css";
@plugin "@tailwindcss/typography";

button {
  cursor: pointer;
}

.dark .ce-inline-toolbar,
.dark .codex-editor--narrow .ce-toolbox,
.dark .ce-conversion-toolbar,
.dark .ce-settings,
.dark .ce-settings__button,
.dark .ce-toolbar__settings-btn,
.dark .cdx-button,
.dark .ce-popover,
.dark .ce-toolbar__plus:hover {
  background: #fff;
  color: #000;
}

.dark .ce-inline-tool,
.dark .ce-conversion-toolbar__label,
.dark .ce-toolbox__button,
.dark .cdx-settings-button,
.dark .ce-toolbar__plus {
  color: #616161;
}

.dark ::selection {
  background: #4d4d4d;
}

.dark .cdx-settings-button:hover,
.dark .ce-settings__button:hover,
.dark .ce-toolbox__button--active,
.dark .ce-toolbox__button:hover,
.dark .cdx-button:hover,
.dark .ce-inline-toolbar__dropdown:hover,
.dark .ce-inline-tool:hover,
.dark .ce-popover__item:hover,
.dark .ce-toolbar__settings-btn:hover {
  background-color: #4d4d4d;
  color: #fff;
}

.dark .cdx-notify--error {
  background: #fb5d5d !important;
}

.dark .cdx-notify__cross::after,
.dark .cdx-notify__cross::before {
  background: #1a1a1a;
}

.image-tool__image {
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 10px;
  padding-bottom: 0;
}
.image-tool--withBackground .image-tool__image,
.editorjs .image-tool--withBackground .image-tool__image {
  padding: 15px;
  @apply bg-elevated;
  margin-top: 2em;
}

.image-tool__image-picture {
  max-width: 100%;
  vertical-align: bottom;
  display: block;
}
.image-tool--withBackground .image-tool__image-picture {
  max-width: 60%;
  margin: 0 auto;
}
.image-tool--withBorder .image-tool__image,
.editorjs .image-tool--withBorder .image-tool__image {
  @apply border border-[var(--ui-border)];
}

.image-tool--stretched .image-tool__image-picture {
  width: 100%;
}
.cdx-input {
  @apply border border-[var(--ui-border)];
}

.ce-block__content {
  max-width: none;
  padding-left: 0.7em;
}

.ce-toolbar__content {
  max-width: none;
}

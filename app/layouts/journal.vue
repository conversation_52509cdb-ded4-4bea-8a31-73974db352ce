<template>
  <div>
    <AppHeader />
    <UContainer class="py-4 md:py-6">
      <div class="grid grid-cols-1 gap-4 md:gap-6 lg:grid-cols-12">
        <!-- Left sidebar navigation -->
        <aside class="hidden lg:col-span-3 lg:block xl:col-span-2">
          <nav class="sticky top-4">
            <ul class="space-y-1">
              <UButton size="xl" class="w-full" variant="ghost" color="neutral" to="/journal">
                <UChip :show="false">
                  <UIcon class="text-xl" name="i-lucide-flame" />
                </UChip>
                Популярное
              </UButton>
              <UButton size="xl" class="w-full" variant="ghost" color="neutral" to="/journal/new">
                <UChip :show="true">
                  <UIcon class="text-xl" name="i-lucide-clock-8" />
                </UChip>
                Свежее
              </UButton>
              <UButton size="xl" class="w-full" variant="ghost" color="neutral" to="/">
                <UChip :show="true">
                  <UIcon class="text-xl" name="i-lucide-clock-8" />
                </UChip>
                Моя лента
              </UButton>
              <UButton size="xl" class="w-full" variant="ghost" color="neutral" to="/">
                <UChip :show="false">
                  <UIcon class="text-xl" name="i-lucide-factory" />
                </UChip>
                Производители
              </UButton>
            </ul>

            <ul class="mt-2 space-y-1">
              <UButton size="xl" class="w-full" variant="ghost" color="neutral" to="/">
                <template #leading>
                  <span class="h-6 w-6 rounded-full bg-cyan-500"></span>
                </template>
                Новости
              </UButton>
              <UButton size="xl" class="w-full" variant="ghost" color="neutral" to="/">
                <template #leading>
                  <span class="h-6 w-6 rounded-full bg-emerald-500"></span>
                </template>
                Личный опыт
              </UButton>
              <UButton size="xl" class="w-full" variant="ghost" color="neutral" to="/">
                <template #leading>
                  <span class="h-6 w-6 rounded-full bg-indigo-500"></span>
                </template>
                Охота
              </UButton>
              <UButton size="xl" class="w-full" variant="ghost" color="neutral" to="/">
                <template #leading>
                  <span class="h-6 w-6 rounded-full bg-lime-500"></span>
                </template>
                Право
              </UButton>
              <UButton size="xl" class="w-full" variant="ghost" color="neutral" to="/">
                <template #leading>
                  <span class="h-6 w-6 rounded-full bg-green-600"></span>
                </template>
                Самооборона
              </UButton>
              <UButton size="xl" class="w-full" variant="ghost" color="neutral" to="/">
                <template #leading>
                  <span class="h-6 w-6 rounded-full bg-green-600"></span>
                </template>
                Клубы
              </UButton>
            </ul>
          </nav>
        </aside>

        <main class="lg:col-span-6 xl:col-span-7">
          <slot />

          <section v-if="false" class="mt-4">
            <div class="grid grid-cols-1 gap-2 lg:grid-cols-3">
              <div v-for="post in gunPosts.data" :key="post.slug">
                <CardItemSmall :item="post" />
              </div>
            </div>
          </section>
        </main>

        <!-- Right sidebar -->
        <aside class="lg:col-span-3 xl:col-span-3">
          <div class="sticky top-4 space-y-3">
            <ModalsCreateArticle />
            <!-- Top blogs -->
            <section v-if="false" class="space-y-3">
              <div class="flex items-center justify-between">
                <h3 class="text-base font-semibold">Топ блогов</h3>
                <NuxtLink to="#" class="text-xs hover:underline">Посмотреть весь топ</NuxtLink>
              </div>
              <div class="space-y-2">
                <UButton
                  v-for="b in topBlogs"
                  :key="b.id"
                  to="#"
                  variant="soft"
                  color="neutral"
                  class="flex"
                >
                  <UAvatar :src="b.avatar" size="sm" />
                  <span class="block">
                    <span class="block truncate font-medium">
                      {{ b.name }}
                      <UBadge
                        v-if="b.verified"
                        icon="i-lucide-badge-check"
                        size="xs"
                        square
                        color="primary"
                        variant="soft"
                      />
                    </span>
                    <span class="block text-xs text-neutral-500"
                      >{{ formatNumber(b.subs) }} подписчиков</span
                    >
                  </span>
                </UButton>
              </div>
            </section>

            <div>
              <AdsVip v-if="gunPosts?.vipAds?.length" :items="gunPosts.vipAds" />
            </div>

            <!-- Popular comments -->
            <section class="space-y-3">
              <h3 class="text-base font-semibold">Популярные комментариии</h3>

              <div class="space-y-3">
                <UButton
                  v-for="(c, i) in comments?.data"
                  :key="i"
                  :to="`/journal/${c.article.slug}`"
                  variant="soft"
                  color="neutral"
                  class="flex p-3"
                >
                  <div class="flex items-start gap-3">
                    <UAvatar as="span" :src="c.user.avatar?.src" size="md" />
                    <span class="block min-w-0 space-y-2">
                      <span class="space-x-1 text-sm leading-tight">
                        <span class="font-medium">{{ c.user.name }}</span>
                        <span class="text-gray-600 dark:text-gray-400">в посте</span>
                        <span class="line-clamp-2">{{ c.article.title }}</span>
                      </span>
                      <span class="mt-2 line-clamp-3 text-xs text-gray-600 dark:text-gray-400">
                        {{ c.text  }}
                      </span>
<!--                      <span v-if="false" class="flex items-center gap-3 text-xs text-neutral-500">-->
<!--                        <span class="inline-flex items-center gap-1"-->
<!--                          >❤️ {{ c.reactions.heart }}</span-->
<!--                        >-->
<!--                        <span class="inline-flex items-center gap-1">😂 {{ c.reactions.lol }}</span>-->
<!--                        <span class="inline-flex items-center gap-1"-->
<!--                          >🔥 {{ c.reactions.fire }}</span-->
<!--                        >-->
<!--                        <span class="inline-flex items-center gap-1"-->
<!--                          >{{ formatNumber(c.reactions.total) }} реакций</span-->
<!--                        >-->
<!--                      </span>-->
                    </span>
                  </div>
                </UButton>
              </div>
            </section>
          </div>
        </aside>
      </div>
    </UContainer>
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { PostsResponse } from "~/types/listing";

const client = useSanctumClient();
const topBlogs = ref([
  {
    id: 1,
    name: "Паша Прав",
    avatar:
      "https://sun6-21.userapi.com/s/v1/ig2/23RVUPxKWmiQceauecXqYEniXjRoJnLd8Kg5KYs7xke8WjSJEIH1lOWHg2Y3k2yATwucHPjOsEMXY2vvOuj5qKc7.jpg?quality=95&crop=82,0,1198,1198&as=32x32,48x48,72x72,108x108,160x160,240x240,360x360,480x480,540x540,640x640,720x720,1080x1080&ava=1&cs=100x100",
    subs: 116,
    verified: true
  },
  {
    id: 3,
    name: "Балистика и патроны",
    avatar: "https://upload.wikimedia.org/wikipedia/commons/4/4f/7.62x39mm.png",
    subs: 2760
  },
  {
    id: 4,
    name: "Оружейный мастер",
    avatar: "https://upload.wikimedia.org/wikipedia/commons/1/12/AK_sights.jpg",
    subs: 950
  }
]);

const { data: gunPosts } = await useAsyncData<PostsResponse>("posts:limit:6", () =>
  client("/posts?limit=6")
);

const { data: comments } = await useAsyncData<PostsResponse>("blog:comments:latest", () =>
  client("/blog/comments")
);

const formatNumber = (n: number) => new Intl.NumberFormat("ru-RU").format(n);
</script>

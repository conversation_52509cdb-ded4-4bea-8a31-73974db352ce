<template>
  <div class="py-3 md:py-4">
    <UContainer>
      <!-- Алерты -->
      <div v-if="data?.is_preview || data?.post?.archived_reason" class="mb-4 space-y-3">
        <UAlert
          v-if="data?.is_preview"
          :ui="{ wrapper: 'justify-center items-center' }"
          color="warning"
          variant="soft"
          icon="i-lucide-circle-alert"
          title="Это предпросмотр объявления, оно может быть еще не опубликовано."
        />
        <UAlert
          v-if="data?.post?.archived_reason"
          :ui="{ wrapper: 'justify-center items-center' }"
          color="error"
          variant="soft"
          icon="i-lucide-circle-alert"
          title="Объявление снято с публикации."
        />
      </div>

      <!-- Заголовок и действия -->
      <div class="mb-4 flex flex-col gap-3 lg:flex-row lg:items-start lg:justify-between">
        <h1 class="text-xl leading-tight font-bold md:text-2xl lg:w-2/3">
          {{ data.post.title }}
        </h1>
        <div v-if="!data.post.archived_reason" class="flex gap-2 lg:w-1/3 lg:justify-end">
          <CardShare :post="data.post" />
          <CardFavorite
            :post="data.post"
            :is_favorite="data?.is_favorite"
            @toggle="toggleFavorite"
          />
        </div>
      </div>

      <!-- Галерея для больших изображений -->
      <div
        v-if="data.post?.thumbnails?.length >= 4"
        class="mb-4"
        :class="{ 'opacity-50': data.post.archived_reason }"
      >
        <ItemGallery :post="data.post" :is_favorite="data?.is_favorite" @toggle="toggleFavorite" />
      </div>

      <!-- Основной контент -->
      <div class="flex flex-col gap-4 lg:flex-row lg:gap-6">
        <!-- Левая колонка -->
        <div class="w-full lg:w-2/3" :class="{ 'opacity-50': data.post.archived_reason }">
          <!-- Галерея для малых изображений -->
          <div
            v-if="data.post?.thumbnails?.length && data.post?.thumbnails?.length < 4"
            class="mb-4"
          >
            <ItemGallery
              :post="data.post"
              :is_favorite="data?.is_favorite"
              @toggle="toggleFavorite"
            />
          </div>

          <!-- Хлебные крошки -->
          <div class="border-b border-gray-200 pb-3 dark:border-gray-800">
            <UBreadcrumb :items="breadcrumbs" />
          </div>

          <!-- Промо блок -->
          <div v-if="data?.is_owner && data.post.moderation_id == 1" class="mt-4">
            <ItemPromo :post="data.post" />
          </div>

          <!-- Админ панель -->
          <div
            v-if="user?.role === 'admin'"
            class="mt-4 border-b border-gray-200 pb-4 dark:border-gray-800"
          >
            <AdminModerations :post="data.post" @refresh="refresh" />
          </div>

          <!-- Информация о продавце -->
          <div v-if="data.post.seller_name" class="mt-4">
            <UUser
              size="lg"
              target="_blank"
              :to="`/user/${data.post.seller_id}`"
              :name="`Продавец: ${data.post.seller_name}`"
              :avatar="{
                src: data.post.seller_avatar,
                alt: data.post.seller_name
              }"
              :description="data.post?.address ?? data.post?.city_name"
            />
          </div>

          <!-- Секции информации -->
          <div class="space-y-0 divide-y divide-gray-200 dark:divide-gray-800">
            <!-- Переоформление -->
            <div v-if="data.post.registration_type" class="py-3">
              <h3 class="mb-2 text-base font-semibold">Переоформление</h3>
              <p class="text-sm">
                Переоформление в
                {{ data.post.registration_type === "OLRR" ? "ЛРО" : "Магазине" }}
              </p>
            </div>

            <!-- Характеристики -->
            <div class="py-3">
              <h3 class="mb-2 text-base font-semibold">Характеристики</h3>
              <ul class="space-y-2 text-sm">
                <li v-if="data.post.is_trade">
                  <span class="font-medium">Возможен обмен</span>
                </li>
                <li v-if="data.post.year">
                  <span class="text-gray-600 dark:text-gray-400">Год выпуска:</span>
                  <span class="ml-2 font-medium">{{ data.post.year }}</span>
                </li>
                <li v-for="attribute in data.post.attributes" :key="attribute.type">
                  <span class="text-gray-600 dark:text-gray-400">{{ attribute.label }}:</span>
                  <span class="ml-2 font-medium">{{ attribute.name }}</span>
                </li>
                <li v-if="data.post.gun_types_name">
                  <span class="text-gray-600 dark:text-gray-400">Тип:</span>
                  <span class="ml-2 font-medium">{{ data.post.gun_types_name }}</span>
                </li>
              </ul>
            </div>

            <!-- Описание -->
            <div class="py-3">
              <h3 class="mb-2 text-base font-semibold">Описание</h3>
              <div class="text-sm whitespace-pre-line">
                {{ data.post.description }}
              </div>
            </div>

            <!-- Место осмотра -->
            <div v-if="data.post?.address && data.post?.address_geo" class="py-3">
              <h3 class="mb-2 text-base font-semibold">Место осмотра</h3>
              <ItemMap :coords="data.post.address_geo" :address="data.post.address" />
            </div>

            <!-- Место переоформления -->
            <div v-if="data.post?.registration_address && data.post?.registration_ge" class="py-3">
              <h3 class="mb-2 text-base font-semibold">
                Переоформление в {{ data.post.registration_type === "OLRR" ? "ЛРО" : "Магазине" }}
              </h3>
              <ItemMap
                :coords="data.post.registration_geo"
                :address="data.post.registration_address"
              />
            </div>

            <!-- Подтверждения и предупреждения -->
            <div class="space-y-3 py-3">
              <ItemIsShop v-if="data.post.seller_is_shop" />
              <ItemZakonConfirm v-else />
              <AlertsSkam />
            </div>
          </div>

          <!-- Мобильная версия контактов -->
          <CardPhoneColumn
            v-if="!data.post.archived_reason"
            :post="data.post"
            class="mt-4 block lg:hidden"
          >
            <div v-if="!data.post.archived_reason">
              <h3 class="mb-2 text-base font-semibold">Спросите у продавца</h3>
              <ItemPageFastMessage :post="data.post" />
            </div>
          </CardPhoneColumn>

          <!-- VIP объявления -->
          <div
            v-if="data?.vipAds?.length"
            class="mt-4 rounded-md bg-blue-50/50 p-3 dark:bg-blue-900/20"
          >
            <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
              <CardItem v-for="(item, n) in data?.vipAds" :key="`vip-${n}`" :item="item" />
            </div>
            <UButton size="xs" to="/promo" block variant="soft" class="mt-3">
              Как попасть сюда?
            </UButton>
          </div>

          <!-- Похожие объявления -->
          <div
            v-if="!data.post.archived_reason && data?.other?.length"
            class="mt-4 border-t border-gray-200 pt-4 dark:border-gray-800"
          >
            <h3 class="mb-2 text-base font-semibold">
              Похожие объявления в разделе {{ data.post.category_name }}, город
              {{ data.post.city_name }}
            </h3>
            <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
              <CardItem v-for="(item, n) in data?.other" :key="`other-${n}`" :item="item" />
            </div>
          </div>
        </div>

        <!-- Правая колонка (боковая панель) -->
        <aside class="w-full space-y-4 lg:w-110 lg:shrink-0">
          <!-- Десктопная версия контактов -->
          <CardPhoneColumn
            v-if="!data.post.archived_reason"
            class="sticky top-4 hidden lg:block"
            :post="data.post"
          >
            <!-- Форма быстрого сообщения -->
            <div v-if="!data.post.archived_reason">
              <h3 class="mb-2 text-base font-semibold">Спросите у продавца</h3>
              <ItemPageFastMessage :post="data.post" />
            </div>
          </CardPhoneColumn>

          <!-- Похожие для архивных -->
          <div v-if="data.post.archived_reason && data?.other?.length">
            <h3 class="mb-2 text-base font-semibold">
              Похожие объявления в разделе {{ data.post.category_name }}, город
              {{ data.post.city_name }}
            </h3>
            <div class="grid gap-3">
              <CardItem
                v-for="(item, n) in data?.other"
                :key="`archived-other-${n}`"
                :item="item"
              />
            </div>
          </div>
        </aside>
      </div>
    </UContainer>
  </div>
</template>

<script setup lang="ts">
import { useSeoFromPost } from "~/composables/useSeoFromPost";
import type { PostResponse } from "~/types/post";
import type { User } from "~/types/auth";
import type { Breadcrumbs, SchemaBreadcrumbItem } from "~/types/breadcrumbs";
import type { SchemaOffer } from "~/types/schema";

const { throw404Error } = useErrorHandling();

const user = useSanctumUser<User>();
const route = useRoute();
const client = useSanctumClient();

const { data, refresh, error } = await useAsyncData<PostResponse>(`post:${route.params.slug}`, () =>
  client(`/posts/${route.params.slug}`, {
    params: {
      ...route.query
    }
  })
);

if (!data.value?.post || error.value) {
  throw404Error();
}

const { title, description, ogImage } = useSeoFromPost(computed(() => data.value?.post));

useSeoMeta({
  title,
  ogTitle: title,
  description,
  ogDescription: description,
  ogImage
});

const toggleFavorite = () => {
  refresh();
};
const breadcrumbs = ref<Breadcrumbs>([]);

if (data.value.post?.city_name && data.value.post?.category_name) {
  breadcrumbs.value = [
    {
      label: data.value.post.category_name,
      to: `/${data.value.post.category}`
    },
    {
      label: data.value.post.city_name,
      to: `/${data.value.post.city_slug}/${data.value.post.category}`
    }
  ];
}

useSchemaOrg([
  defineBreadcrumb({
    itemListElement: [
      ...(breadcrumbs.value?.map(
        (item): SchemaBreadcrumbItem => ({
          name: item.label,
          item: item.to
        })
      ) ?? {
        name: "Главная",
        item: "/"
      }),
      { name: data.value.post.title }
    ]
  }),
  defineOffer({
    name: data.value.post.title,
    description: data.value.post.description_short ?? data.value.post.description,
    image: data.value.post.thumbnails,
    price: data.value.post.price,
    priceCurrency: "RUB",
    category: data.value.post.category_name,
    seller: {
      name: data.value.post.seller_name,
      image: data.value.post.seller_avatar
    }
  } as SchemaOffer)
]);
</script>
